/*
    filename: stack.h
    this file contains declaration of stack structure and stack functionalties

====================================================================== */


#ifndef __STACK_H
#define __STACK_H


#define SIZE 10

typedef struct
{
    int arr[ SIZE ];//20 bytes
    int top;//4 bytes
}stack_t;

//sizeo(stack_t): 24 bytes

typedef int bool_t;

//declaration of stack functionalities
void init_stack(stack_t *ps);
bool_t is_stack_full(stack_t *ps);
bool_t is_stack_empty(stack_t *ps);
void push_element(stack_t *ps, int ele);
void pop_element(stack_t *ps);
int peek_element(stack_t *ps);

#endif