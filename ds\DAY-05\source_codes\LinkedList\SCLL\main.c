//implementation of singly circular linked list operations:

#include<stdio.h>
#include<stdlib.h>

//definition of node structure
typedef struct node
{
    int data;
    struct node *next;
}node_t;

//definition of list structure
typedef struct 
{
    node_t *head;
    int cnt;
}list_t;


typedef int bool_t;

//declarations of list functionalities
void init_list(list_t *list);
bool_t is_list_empty(list_t *list);
node_t *create_node(int data);
void add_node_at_last_position(list_t *list, int data);
void add_node_at_first_position(list_t *list, int data);
void add_node_at_specific_position(list_t *list, int pos, int data);
void delete_node_at_first_position(list_t *list);
void delete_node_at_last_position(list_t *list);
void delete_node_at_specific_position(list_t *list, int pos);
void display_list(list_t *list);
void free_list(list_t *list);

enum menu_options{ EXIT, ADDLAST, ADDFIRST, ADDATPOS, DELFIRST, DELLAST, DELATPOS, DISP };

int menu( void )
{
    int choice;

    printf("********** singly circular linked list **********\n");
    printf("0. exit\n");
    printf("1. add node at last position\n");
    printf("2. add node at first position\n");
    printf("3. add node at specific position\n");
    printf("4. delete node at first position\n");
    printf("5. delete node at last position\n");
    printf("6. delete node at specific position\n");
    
    printf("enter the choice: ");
    scanf("%d", &choice);

    return choice;
}

//entry point function
int main(void)
{
    int pos;
    int data;

    list_t list1;

    init_list(&list1);

    while( 1 )
    {
        int choice = menu( );
        switch (choice)
        {
        case EXIT:
            free_list(&list1);
            exit(0);

        case ADDLAST:
            printf("enter the data : ");
            scanf("%d", &data);
            add_node_at_last_position(&list1, data);
            break;

        case ADDFIRST:
            printf("enter the data : ");
            scanf("%d", &data);
            add_node_at_first_position(&list1, data);
            break;

        case ADDATPOS:
            while( 1 )
            {
                //accept position from the user
                printf("enter the position: ");
                scanf("%d", &pos);

                //validate position
                //pos is valid only if it is in between 1 to cnt+1
                if( pos >= 1 && pos <= list1.cnt + 1 )
                    break;

                printf("invalid position ...\n");
            }

            printf("enter the data : ");
            scanf("%d", &data);
            add_node_at_specific_position(&list1, pos, data);
            break;

        case DELFIRST:
            delete_node_at_first_position(&list1);
            break;

        case DELLAST:
            delete_node_at_last_position(&list1);
            break;

        case DELATPOS:
            while( 1 )
            {
                //accept position from the user
                printf("enter the position: ");
                scanf("%d", &pos);

                //validate position
                //pos is valid only if it is in between 1 to cnt
                if( pos >= 1 && pos <= list1.cnt )
                    break;

                printf("invalid position ...\n");
            }

            delete_node_at_specific_position(&list1, pos);

            break;

        case DISP:
            display_list(&list1);
            break;

        }//end of switch control block
    }//end of while loop
    
    return 0;
}

//definitions of list functionalities
void init_list(list_t *list)
{
    list->head = NULL;
    list->cnt = 0;
}

bool_t is_list_empty(list_t *list)
{
    return ( list->head == NULL );
}

void display_list(list_t *list)
{
    //if list is not empty
    if( !is_list_empty(list) )
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node including it
        
        printf("head -> ");
        do
        {
            printf("%d -> ", trav->data);//display data part of node
            trav = trav->next;
        }while( trav != list->head );
        printf(" null\n");
        //printf("no. of nodes in a list are: %d\n", count_nodes(list) ); //=> O(n)
        printf("no. of nodes in a list are: %d\n", list->cnt );//=> O(1)
    }
    else
        printf("list is empty !!!\n");
}

node_t *create_node(int data)
{
    //1. allocate memory dynamically for a node
    node_t *temp = (node_t *)malloc( sizeof(node_t) );
    /* on success, malloc() function allocates requested bytes of memory from heap section and
    it returns starting addr of dynamically allocated block and on failure it returns NULL */
    if( temp == NULL )
    {
        perror("malloc() failed !!!\n");
        exit(1);
    }

    //2. initialize members of the node
    temp->data = data;
    temp->next = NULL;

    //3. return an addr of dynamically allocated node/block to the calling function
    return temp;//by meansreturninig value of pointer var we are returnning addr
}

void add_node_at_last_position(list_t *list, int data)
{
    //step-1: create a newnode
    node_t *newnode = create_node(data);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        //store an addr first node into the next part newly added node at last
        newnode->next = list->head;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node
        while( trav->next != list->head )
        {
            trav = trav->next;//move trav towards its next node
        }

        /* attach newly created node to the last node i.e. to store an addr of newly created node
        into the next part of last node */
        trav->next = newnode;
        //store an addr first node into the next part newly added node at last
        newnode->next = list->head;
        list->cnt++;
       }
}

void add_node_at_first_position(list_t *list, int data)
{
    //step-1: create a newnode
    node_t *newnode = create_node(data);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        //store an addr first node into the next part newly added node at last
        newnode->next = list->head;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node
        while( trav->next != list->head )
        {
            trav = trav->next;//move trav towards its next node
        }

        //store an addr of cur first node into the next part of newly created node
        newnode->next = list->head;
        //attach newly created node to the head
        list->head = newnode;
        //update next part of last node by newly added node at first position
        trav->next = list->head;
        list->cnt++;
       }
}

void add_node_at_specific_position(list_t *list, int pos, int data)
{
    if( pos == 1 )//if pos is the first position
        add_node_at_first_position(list, data);
    else if( pos == list->cnt + 1 )//if pos is the last position
        add_node_at_last_position(list, data);
    else//if pos is the in between position
    {
        //create a newnode
        node_t *newnode = create_node(data);
        int i=1;
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till (pos-1)th node
        while( i < pos-1 ){
            i++;
            trav = trav->next;
        }
        //store an addr of cur (pos)th node into next part of newly created node
        newnode->next = trav->next;
        //store an addr newly created node into the next part of (pos-1)th node
        trav->next = newnode;
        list->cnt++;
    }
}

void delete_node_at_first_position(list_t *list)
{
    //step-1: check list is not empty
    //if list is not empty then only we can delete node from it
    if( !is_list_empty(list) )
    {
        //step-2: if list contains only one node 
        if( list->head->next == list->head )
        {
            //delete the node and make head as NULL
            free(list->head);//free(1000);
            list->head = NULL;
            list->cnt=0;
        } 
        else//step-3: if list contains more than one nodes
        {
            //start traversal from first node
            node_t *trav = list->head;
            node_t *temp = NULL;
            //traverse the list till last node
            while( trav->next != list->head )
            {
                trav = trav->next;//move trav towards its next node
            }

            //store an addr of cur first node into the temp which is to be deleted
            temp = list->head;
            //attach cur second node to the head
            list->head = list->head->next;
            //update next part of last node
            trav->next = list->head;
            //delete the node
            free(temp);
            temp = NULL;
            list->cnt--;
        }
    }
    else
        printf("list is empty !!!\n");
}

void delete_node_at_last_position(list_t *list)
{
    //step-1: check list is not empty
    //if list is not empty then only we can delete node from it
    if( !is_list_empty(list) )
    {
        //step-2: if list contains only one node 
        if( list->head->next == list->head )
        {
            //delete the node and make head as NULL
            free(list->head);//free(1000);
            list->head = NULL;
            list->cnt=0;
        } 
        else//step-3: if list contains more than one nodes
        {
            //start traversal from first node
            node_t *trav = list->head;
            node_t *temp = NULL;
            //traverse the list till second last node
            while( trav->next->next != list->head )
            {
                trav = trav->next;//move trav towards its next node
            }

            //delete the last node
            free(trav->next);
            //store an addr of first node into the next part of cur second last node
            trav->next = list->head;
            list->cnt--;
        }
    }
    else
        printf("list is empty !!!\n");
}

void free_list(list_t *list)
{
    //if list is not empty
    if( !is_list_empty(list) )
    {
        while( !is_list_empty(list) )//while list not becomes empty
            delete_node_at_last_position(list);
            //delete_node_at_first_position(list);//delete all the nodes from it one by one 

        printf("list freed successfully ...\n");
    }
    else
        printf("list is empty !!!\n");
}

void delete_node_at_specific_position(list_t *list, int pos)
{
    if( pos == 1 )//if pos is the first position
        delete_node_at_first_position(list);
    else if( pos == list->cnt )//if pos is the last position
        delete_node_at_last_position(list);
    else//if pos is in between position
    {
        int i=1;
        //start traversal from first node
        node_t *trav = list->head;
        node_t *temp = NULL;

        //traverse the list till (pos-1)th node
        while( i < pos-1 ){
            i++;
            trav = trav->next;
        }
        //store an addr of the node into the temp which is to be deleted
        temp=trav->next;
        //store an addr of (pos+1)th node into the next part of (pos-1)th node
        trav->next = trav->next->next;
        //delete the node
        free(temp);
        temp=NULL;
        list->cnt--;
    }
}



