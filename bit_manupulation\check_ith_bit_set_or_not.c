/*check ith bit set or not */

#include<stdio.h>
#include<stdbool.h>

bool checksetbit(int num ,int pos)  // 1101
{
    //method 1 uisng left shift operator
//    int mask =  1<<pos;

//    return (num & mask);

//method-2 using right shift operator
int mask = num >> pos;

return (mask & 1);
 

}

int main()
{
    int num = 13;
    int i = 1;

    bool result = checksetbit(num ,i);
    if(result)
    {
        printf("this  %d bit is set ",i);
    }
    else
    {
        printf("this  %d bit is not set ",i);
    }

    return 0;
}