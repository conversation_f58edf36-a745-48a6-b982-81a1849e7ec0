/* 
    searching algorithms implementation:
    
    1. linear search
    2. binary search
==================================================================*/

#include<stdio.h>

#define SIZE 10

#define true 1
#define false 0

typedef int bool_t;

void display_array_elements(int arr[]);//arr -> pointer to 1-d array
int comparisons = 0;
bool_t linear_search(int arr[], int key );
//bool_t rec_linear_search( int arr[], int key , int index );
int rec_linear_search( int arr[], int key , int index );
bool_t binary_search( int arr[], int key );

int main(void)
{
    //int arr[ SIZE ] = { 90, 70, 80, 10, 20, 30, 60, 40, 50, 100 };
    int arr[ SIZE ] = { 10, 20, 30, 40, 50, 60, 70, 80, 90, 100 };
    int key;
    int index;

    display_array_elements(arr);

    printf("enter the key: ");
    scanf("%d", &key);

    if( binary_search( arr, key ) )
        printf("%d is found in an array, no. of comparisons = %d\n", key, comparisons);
    else    
        printf("%d is not found in an array, no. of comparisons = %d\n", key, comparisons);

    /*
    if( linear_search( arr, key ) )
        printf("%d is found in an array, no. of comparisons = %d\n", key, comparisons);
    else    
        printf("%d is not found in an array, no. of comparisons = %d\n", key, comparisons);
    */

    /*
    if( rec_linear_search( arr, key , 0 ) )//1. initialization : index = 0
        printf("%d is found in an array, no. of comparisons = %d\n", key, comparisons);
    else    
        printf("%d is not found in an array, no. of comparisons = %d\n", key, comparisons);
    */

    /*
    if( ( index = rec_linear_search( arr, key , 0 ) ) != -1 )//1. initialization : index = 0
        printf("%d is found in an array, at index = %d\n", key, index );
    else    
        printf("%d is not found in an array, index = %d\n", key, index );
    */

    return 0;
}

void display_array_elements(int arr[])
{
    int index;
    printf("array ele's are: ");
    for( index = 0 ; index < SIZE ; index++ ){
        printf("%4d", arr[ index ] );
    }
    printf("\n");
}

bool_t linear_search(int arr[], int key )
{
    int index;
    comparisons=0;

    for( index = 0 ; index < SIZE ; index++ ){
        comparisons++;
        if( key == arr[ index ] )
            return true;//key is found        
    }

    return false;//key is not found
}

/*
bool_t rec_linear_search( int arr[], int key , int index )
{
    //base condition
    if( index == SIZE )
        return false;//key is not found

    comparisons++;
    if( key == arr[ index ] )
        return true;
    else
        return ( rec_linear_search(arr, key, index+1 ) );//modification
}
*/

int rec_linear_search( int arr[], int key , int index )
{
    //base condition
    if( index == SIZE )
        return -1;//key is not found

    //comparisons++;
    if( key == arr[ index ] )
        return index;
    else
        return ( rec_linear_search(arr, key, index+1 ) );//modification
}

bool_t binary_search( int arr[], int key )
{
    int left = 0;
    int right = SIZE-1;
    comparisons=0;

    //repeat the logic till either key is not found or subarray is valid
    while( left <= right )//if ( left > right ) => array is invalid -> loop gets break
    {    
        int mid = (left + right)/2;//calculate mid pos

        comparisons++;
        if( key == arr[ mid ] )//compare value of key with an ele which is at mid pos
            return true;

        if( key < arr[ mid ] )
            right = mid-1;//goto search key into the left subarray
        else
            left = mid+1;//goto search key into the right subarray
    }

    return false;//key is not found

}























