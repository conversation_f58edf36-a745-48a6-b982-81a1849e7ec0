//implementation of singly linear linked list operations:

#include<stdio.h>
#include<stdlib.h>

typedef int bool_t;

//node structure
typedef struct node
{
	int data;//4 bytes
	struct node *next;//self referential pointer => 4 bytes
}node_t;

//list structure
typedef struct
{
    node_t *head;//4 bytes    
    int cnt;
}list_t;

//declaration of list functionalities:
void init_list(list_t *list);
void add_node_at_last_position(list_t *list, int data);
void add_node_at_first_position(list_t *list, int data);
void add_node_at_specific_position(list_t *list, int pos, int data);
node_t *create_node(int data);
bool_t is_list_empty(list_t *list);
void display_list(list_t *list);
int count_nodes(list_t *list);


int main(void)
{
    //printf("sizeof(struct node): %d bytes\n", sizeof(struct node));
    //printf("sizeof(node_t): %d bytes\n", sizeof(node_t));
    int pos;
    list_t list1;
    //list_t list2;

    init_list(&list1);
    //init_list(&list2);

    add_node_at_last_position(&list1, 10);
    add_node_at_last_position(&list1, 20);
    add_node_at_last_position(&list1, 30);
    add_node_at_last_position(&list1, 40);
    add_node_at_last_position(&list1, 50);
    add_node_at_last_position(&list1, 60);
    add_node_at_last_position(&list1, 70);
    add_node_at_last_position(&list1, 80);

    
   /*
   add_node_at_first_position(&list1, 100);
   add_node_at_first_position(&list1, 200);
   add_node_at_first_position(&list1, 300);
   add_node_at_first_position(&list1, 400);
   add_node_at_first_position(&list1, 500);
   add_node_at_first_position(&list1, 600);
   add_node_at_first_position(&list1, 700);
    */

    printf("list1 : "); display_list(&list1);

    
    while( 1 )
    {
        //accept position from the user
        printf("enter the position: ");
        scanf("%d", &pos);

        //validate position
        //pos is valid only if it is in between 1 to cnt+1
        if( pos >= 1 && pos <= list1.cnt + 1 )
            break;

        printf("invalid position ...\n");
    }

    add_node_at_specific_position(&list1, pos, 99);

    display_list(&list1);


    /*
    add_node_at_last_position(&list2, 11);
    add_node_at_last_position(&list2, 22);
    add_node_at_last_position(&list2, 33);
    add_node_at_last_position(&list2, 44);
    add_node_at_last_position(&list2, 55);
    add_node_at_last_position(&list2, 66);

    printf("list2 : "); display_list(&list2);
    */
    //add_node_at_first_position(&list1, 5);

    //printf("list1 : "); display_list(&list1);

    return 0;
}

//definitions of list functionalities:
void init_list(list_t *list)
{
    list->head = NULL;
    list->cnt = 0;
}

bool_t is_list_empty(list_t *list)
{
    return ( list->head == NULL );
}

int count_nodes(list_t *list)
{
    int cnt = 0;

    if( !is_list_empty(list) )
    {
        //start traversal of the list from first node
        node_t *trav = list->head;
        //traverse the list till last node inclusind it
        while( trav != NULL )
        {
            cnt++;
            trav = trav->next;
        }
    }

    return cnt;
}


void display_list(list_t *list)
{
    //if list is not empty
    if( !is_list_empty(list) )
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node including it
        
        printf(" head -> ");
        while( trav != NULL )
        {
            printf("%d -> ", trav->data);//display data part of node
            trav = trav->next;
        }
        printf(" null\n");
        //printf("no. of nodes in a list are: %d\n", count_nodes(list) ); //=> O(n)
        printf("no. of nodes in a list are: %d\n", list->cnt );//=> O(1)
    }
    else
        printf("list is empty !!!\n");
}

node_t *create_node(int data)
{
    //1. allocate memory dynamically for a node
    node_t *temp = (node_t *)malloc( sizeof(node_t) );
    /* on success, malloc() function allocates requested bytes of memory from heap section and
    it returns starting addr of dynamically allocated block and on failure it returns NULL */
    if( temp == NULL )
    {
        perror("malloc() failed !!!\n");
        exit(1);
    }

    //2. initialize members of the node
    temp->data = data;
    temp->next = NULL;

    //3. return an addr of dynamically allocated node/block to the calling function
    return temp;//by meansreturninig value of pointer var we are returnning addr
}

void add_node_at_last_position(list_t *list, int data)
{
    //step-1: create a newnode
    node_t *newnode = create_node(data);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node
        while( trav->next != NULL )
        {
            trav = trav->next;//move trav towards its next node
        }

        /* attach newly created node to the last node i.e. to store an addr of newly created node
        into the next part of last node */
        trav->next = newnode;
        list->cnt++;
       }
}

void add_node_at_first_position(list_t *list, int data)
{
    //step-1: create a newnode
    node_t *newnode = create_node(data);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //store an addr of cur first node into the next part of newly created node
        newnode->next = list->head;
        //attach newly created node to the head
        list->head = newnode;
        list->cnt++;
    }
}

void add_node_at_specific_position(list_t *list, int pos, int data)
{
    if( pos == 1 )//if pos is the first position
        add_node_at_first_position(list, data);
    else if( pos == list->cnt + 1 )//if pos is the last position
        add_node_at_last_position(list, data);
    else//if pos is the in between position
    {
        //create a newnode
        node_t *newnode = create_node(data);
        int i=1;
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till (pos-1)th node
        while( i < pos-1 ){
            i++;
            trav = trav->next;
        }
        //store an addr of cur (pos)th node into next part of newly created node
        newnode->next = trav->next;
        //store an addr newly created node into the next part of (pos-1)th node
        trav->next = newnode;
        list->cnt++;
    }
}














