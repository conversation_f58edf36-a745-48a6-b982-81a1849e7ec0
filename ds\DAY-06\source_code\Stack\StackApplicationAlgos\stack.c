
/*
    filename: stack.c
    this file contains definitions stack functionalties
====================================================================== */

#include"stack.h"

//definitions of stack functionalities
void init_stack(stack_t *ps)
{
    ps->top = -1;   
}

bool_t is_stack_full(stack_t *ps)
{
    return ( ps->top == SIZE-1 );
}

bool_t is_stack_empty(stack_t *ps)
{
    return ( ps->top == -1 );
}

void push_element(stack_t *ps, int ele)
{
    //step-2: increment the value of top by 1
    ps->top++;    
    //step-3: insert/add an element onto the stack from top end
    ps->arr[ ps->top ] = ele;
}

void pop_element(stack_t *ps)
{
    //step-2: decrement the value of top by 1
    //[ by means of decrementing value of top by 1, we are achieving deletion of an element from stack ( and not from array – i.e. ele is not getting deleted physically) ].
    ps->top--;
}

int peek_element(stack_t *ps)
{
    //step-2: get the value of an element which is at top end (without increment / decrement top ).
    return ( ps->arr[ ps->top ] );
}
