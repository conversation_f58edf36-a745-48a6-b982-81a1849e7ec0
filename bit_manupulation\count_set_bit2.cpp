#include <iostream>
using namespace std;
int setbit(int n)
{
    int cnt = 0;
    while (n != 0)   // 101 = 5   101 & 001 = 1 , cnt = 1 ,n  =10= 2 , 10 & 01 = 0 , cnt = 1 , n = 1 , 1 & 1 = 1 , cnt = 2 , n = 0 
    {
        int rem = n & 1;
        if (rem == 1)
        {
            cnt = cnt + 1;
        }
        n = n >> 1;
    }
    return cnt;
}

int main()
{
    int n;
    cin >> n;
    int result = setbit(n);
    cout << "setbit is = " << result << endl;

    return 0;
}
