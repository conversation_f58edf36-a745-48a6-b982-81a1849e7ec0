//implementation of linked list of employees:
#include<stdio.h>
#include<stdlib.h>

typedef struct
{
    int empid;//4 bytes
    char name[ 32 ];//32 bytes
    float salary; //4 bytes
}emp_t;

//sizeof(emp_t): 40 bytes
//definition of node structure
typedef struct node
{
    struct node *prev;//4 bytes
    emp_t data;//40 bytes
    struct node *next;//4 bytes
}node_t;

//sizeof(struct node):  48 bytes

//definition of list structure
typedef struct 
{
    node_t *head;
    int cnt;
}list_t;


typedef int bool_t;

//declarations of list functionalities
void init_list(list_t *list);
bool_t is_list_empty(list_t *list);
node_t *create_node(emp_t *pe);
void add_node_at_last_position(list_t *list, emp_t *pe);
void display_list(list_t *list);


void accept_employee_record(emp_t *pe);
void display_employee_record(emp_t *pe);

//entry point function
int main(void)
{
    list_t emp_list;

    emp_t e1 = { 1, "sachin", 9999.99 };
    emp_t e2 = { 2, "devendra", 8888.88 };
    emp_t e3 = { 3, "ganesh", 7777.77 };

    init_list(&emp_list);


    add_node_at_last_position(&emp_list, &e1);
    add_node_at_last_position(&emp_list, &e2);
    add_node_at_last_position(&emp_list, &e3);

    display_list(&emp_list);


    return 0;
}

//definitions of list functionalities
void init_list(list_t *list)
{
    list->head = NULL;
    list->cnt = 0;
}

bool_t is_list_empty(list_t *list)
{
    return ( list->head == NULL );
}

void display_list(list_t *list)
{
    //if list is not empty
    if( !is_list_empty(list) )
    {
        //start traversal from first node
        node_t *trav = list->head;
        node_t *temp = NULL;
        //traverse the list till last node including it
        
        printf("list of employess is :\n");
        while( trav != NULL )
        {
            temp = trav;
            //display data part of node
            display_employee_record(&trav->data);
            trav = trav->next;
        }
        //printf("no. of nodes in a list are: %d\n", count_nodes(list) ); //=> O(n)
        printf("no. of nodes in a list are: %d\n", list->cnt );//=> O(1)
    }
    else
        printf("list is empty !!!\n");
}

node_t *create_node(emp_t *pe)
{
    //1. allocate memory dynamically for a node
    node_t *temp = (node_t *)malloc( sizeof(node_t) );
    /* on success, malloc() function allocates requested bytes of memory from heap section and
    it returns starting addr of dynamically allocated block and on failure it returns NULL */
    if( temp == NULL )
    {
        perror("malloc() failed !!!\n");
        exit(1);
    }

    //2. initialize members of the node
    temp->data = *pe;
    temp->next = NULL;
    temp->prev = NULL;

    //3. return an addr of dynamically allocated node/block to the calling function
    return temp;//by meansreturninig value of pointer var we are returnning addr
}

void add_node_at_last_position(list_t *list, emp_t *pe)
{
    //step-1: create a newnode
    node_t *newnode = create_node(pe);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node
        while( trav->next != NULL )
        {
            trav = trav->next;//move trav towards its next node
        }

        /* attach newly created node to the last node i.e. to store an addr of newly created node
        into the next part of last node */
        trav->next = newnode;
        //store an addr of cur last node into the prev part of newly created node
        newnode->prev = trav;
        list->cnt++;
       }
}

void accept_employee_record(emp_t *pe)
{
    printf("enter empid, name & salary: ");
    scanf("%d %s %f", &pe->empid, pe->name, &pe->salary );
}

void display_employee_record(emp_t *pe)
{
    printf("%-10d %-20s %-10.2f\n", pe->empid, pe->name, pe->salary);
}

