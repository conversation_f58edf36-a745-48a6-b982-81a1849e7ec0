/* filename: list.c
	this file contains definitions of list functionalities
======================================================================================  */


//definitions of list functionalities:
#include<stdio.h>
#include<stdlib.h>

#include"list.h"

void init_list(list_t *list)
{
    list->head = NULL;
    list->cnt = 0;
}

bool_t is_list_empty(list_t *list)
{
    return ( list->head == NULL );
}

int count_nodes(list_t *list)
{
    int cnt = 0;

    if( !is_list_empty(list) )
    {
        //start traversal of the list from first node
        node_t *trav = list->head;
        //traverse the list till last node inclusind it
        while( trav != NULL )
        {
            cnt++;
            trav = trav->next;
        }
    }

    return cnt;
}


void display_list(list_t *list)
{
    //if list is not empty
    if( !is_list_empty(list) )
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node including it
        
        printf("head -> ");
        while( trav != NULL )
        {
            printf("%d -> ", trav->data);//display data part of node
            trav = trav->next;
        }
        printf(" null\n");
        //printf("no. of nodes in a list are: %d\n", count_nodes(list) ); //=> O(n)
        printf("no. of nodes in a list are: %d\n", list->cnt );//=> O(1)
    }
    else
        printf("list is empty !!!\n");
}

node_t *create_node(int data)
{
    //1. allocate memory dynamically for a node
    node_t *temp = (node_t *)malloc( sizeof(node_t) );
    /* on success, malloc() function allocates requested bytes of memory from heap section and
    it returns starting addr of dynamically allocated block and on failure it returns NULL */
    if( temp == NULL )
    {
        perror("malloc() failed !!!\n");
        exit(1);
    }

    //2. initialize members of the node
    temp->data = data;
    temp->next = NULL;

    //3. return an addr of dynamically allocated node/block to the calling function
    return temp;//by meansreturninig value of pointer var we are returnning addr
}

void add_node_at_last_position(list_t *list, int data)
{
    //step-1: create a newnode
    node_t *newnode = create_node(data);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node
        while( trav->next != NULL )
        {
            trav = trav->next;//move trav towards its next node
        }

        /* attach newly created node to the last node i.e. to store an addr of newly created node
        into the next part of last node */
        trav->next = newnode;
        list->cnt++;
       }
}

void add_node_at_first_position(list_t *list, int data)
{
    //step-1: create a newnode
    node_t *newnode = create_node(data);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //store an addr of cur first node into the next part of newly created node
        newnode->next = list->head;
        //attach newly created node to the head
        list->head = newnode;
        list->cnt++;
    }
}

void add_node_at_specific_position(list_t *list, int pos, int data)
{
    if( pos == 1 )//if pos is the first position
        add_node_at_first_position(list, data);
    else if( pos == list->cnt + 1 )//if pos is the last position
        add_node_at_last_position(list, data);
    else//if pos is the in between position
    {
        //create a newnode
        node_t *newnode = create_node(data);
        int i=1;
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till (pos-1)th node
        while( i < pos-1 ){
            i++;
            trav = trav->next;
        }
        //store an addr of cur (pos)th node into next part of newly created node
        newnode->next = trav->next;
        //store an addr newly created node into the next part of (pos-1)th node
        trav->next = newnode;
        list->cnt++;
    }
}

void delete_node_at_first_position(list_t *list)
{
    //step-1: check list is not empty
    //if list is not empty then only we can delete node from it
    if( !is_list_empty(list) )
    {
        //step-2: if list contains only one node 
        if( list->head->next == NULL )
        {
            //delete the node and make head as NULL
            free(list->head);//free(1000);
            list->head = NULL;
            list->cnt=0;
        } 
        else//step-3: if list contains more than one nodes
        {
            //store an addr of first node into a temp which is to be deleted
            node_t *temp = list->head;
            //attach cur second node to the head
            list->head = list->head->next;
            //delete the node
            free(temp);//free(1000);
            temp=NULL;//to avoid dangling pointer make temp as NULL
            list->cnt--;
        }
    }
    else
        printf("list is empty !!!\n");
}

void delete_node_at_last_position(list_t *list)
{
    //step-1: check list is not empty
    //if list is not empty then only we can delete node from it
    if( !is_list_empty(list) )
    {
        //step-2: if list contains only one node 
        if( list->head->next == NULL )
        {
            //delete the node and make head as NULL
            free(list->head);//free(1000);
            list->head = NULL;
            list->cnt=0;
        } 
        else//step-3: if list contains more than one nodes
        {
            //start traversal of the list from first node
            node_t *trav=list->head;
            //traverse the list till second last node
            while( trav->next->next != NULL )
                trav = trav->next;//move trav towards its next node

            //delete the last node from second last node
            free(trav->next);//free(5000);
            //make next part of cur second last node as NULL
            trav->next = NULL;

            list->cnt--;
        }
    }
    else
        printf("list is empty !!!\n");
}


void free_list(list_t *list)
{
    //if list is not empty
    if( !is_list_empty(list) )
    {
        while( !is_list_empty(list) )//while list not becomes empty
            delete_node_at_last_position(list);
            //delete_node_at_first_position(list);//delete all the nodes from it one by one 

        printf("list freed successfully ...\n");
    }
    else
        printf("list is empty !!!\n");
}

void delete_node_at_specific_position(list_t *list, int pos)
{
    if( pos == 1 )//if pos is the first position
        delete_node_at_first_position(list);
    else if( pos == list->cnt )//if pos is the last position
        delete_node_at_last_position(list);
    else//if pos is in between position
    {
        int i=1;
        //start traversal from first node
        node_t *trav = list->head;
        node_t *temp = NULL;

        //traverse the list till (pos-1)th node
        while( i < pos-1 ){
            i++;
            trav = trav->next;
        }
        //store an addr of the node into the temp which is to be deleted
        temp=trav->next;
        //store an addr of (pos+1)th node into the next part of (pos-1)th node
        trav->next = trav->next->next;
        //delete the node
        free(temp);
        temp=NULL;
        list->cnt--;
    }
}

void display_list_reverse(list_t *list, node_t *trav)
{
    //base condition
    if( trav == NULL )
        return;

    display_list_reverse(list, trav->next);//modification
    printf("%4d", trav->data);
}


void reverse_list(list_t *list)
{
    node_t *t1 = list->head;
    node_t *t2 = t1->next;
    
    //last node's next part will be NULL
    t1->next=NULL;

    while( t2 != NULL )
    {
        node_t *t3 = t2->next;
        t2->next = t1;//reverse the link between pair of nodes

        t1 = t2;//move t1 towards its next node
        t2 = t3;//move t2 towards its next node
    }

    //store an addr of new first node (which was prev last node) into the head
    list->head = t1;
}


/* search_node() : 
    if node is found => it should returns an addr of the node as well as addr of prev node
    if node is not found => NULL
*/
node_t *seach_node(list_t *list, node_t **prev, int data)
{
    node_t *trav = NULL;
    for( trav = list->head ; trav != NULL ; trav = trav->next )
    {
        
        if( data == trav->data )
            return trav;//return an addr of node 

        *prev = trav;
    }
    *prev = NULL;
    return NULL;
}

bool_t search_and_delete(list_t *list, int data)
{
    //first we have to search node into the list
    node_t *prev = NULL;
    node_t *temp = seach_node(list, &prev, data);//prev pointer we are passing as an out param
    
    
    if( temp == NULL )//node is not found
        return 0;

    printf("temp->data: %d\n", temp->data);
    if( prev != NULL )
        printf("prev->data: %d\n", prev->data);


    //if node is found delete it
    //store an addr of next node of the node which is to be deleted into next part of its prev
    prev->next = temp->next;
    free(temp);
    temp = NULL;
    list->cnt--;
    return 1;//node found and deleted sucessfully
}




















