//implementation of singly linear linked list operations:

#include<stdio.h>
#include<stdlib.h>

#include"list.h"

int menu(void)
{
    int choice;

    printf("***** sinlgly linear linked list *****\n");
    printf("0. exit\n");
    printf("1. add node at last position\n");
    printf("2. add node at first position\n");
    printf("3. add node at specific position\n");
    printf("4. delete node at first position\n");
    printf("5. delete node at last position\n");
    printf("6. delete node at specific position\n");
    printf("7. display list\n");
    printf("8. display list in a reverse order\n");
    printf("9. reverse the list\n");
    printf("10. search & delete\n");
    printf("enter the choice: ");
    scanf("%d", &choice);

    return choice;
}

enum menu_options{ EXIT, ADDLAST, ADDFIRST, ADDATPOS, DELFIRST, DELLAST, DELATPOS, DISPLIST,
DISPLISTREV, REVLIST, SEARCH_DEL };

int main(void)
{
    int pos;
    int data;

    list_t list1;

    init_list(&list1);

    while( 1 )
    {
        int choice = menu( );
        switch (choice)
        {
        case EXIT:
            free_list(&list1);
            exit(0);

        case ADDLAST:
            printf("enter the data : ");
            scanf("%d", &data);
            add_node_at_last_position(&list1, data);
            break;

        case ADDFIRST:
            printf("enter the data : ");
            scanf("%d", &data);
            add_node_at_first_position(&list1, data);
            break;

        case ADDATPOS:
            while( 1 )
            {
                //accept position from the user
                printf("enter the position: ");
                scanf("%d", &pos);

                //validate position
                //pos is valid only if it is in between 1 to cnt+1
                if( pos >= 1 && pos <= list1.cnt + 1 )
                    break;

                printf("invalid position ...\n");
            }

            printf("enter the data : ");
            scanf("%d", &data);
            add_node_at_specific_position(&list1, pos, data);
            break;

        case DELFIRST:
            delete_node_at_first_position(&list1);
            break;

        case DELLAST:
            delete_node_at_last_position(&list1);
            break;

        case DELATPOS:
            while( 1 )
            {
                //accept position from the user
                printf("enter the position: ");
                scanf("%d", &pos);

                //validate position
                //pos is valid only if it is in between 1 to cnt
                if( pos >= 1 && pos <= list1.cnt )
                    break;

                printf("invalid position ...\n");
            }

            delete_node_at_specific_position(&list1, pos);

            break;

        case DISPLIST:
            display_list(&list1);
            break;

        case DISPLISTREV:
            printf("list in a reverse order is: ");
            display_list_reverse(&list1, list1.head);//initialization
            printf("\n");
            break;

        case REVLIST:
            reverse_list(&list1);
            break;

        case SEARCH_DEL:
            printf("enter data part of the node which is to deleted: ");
            scanf("%d", &data);

            if( search_and_delete(&list1, data) )
                printf("node having data part : %d deleted from the list successfully...\n", data);
            else
                printf("node having data part : %d not found in a list\n", data);
            break;

        }//end of switch control block
    }//end of while loop
    
    return 0;
}

