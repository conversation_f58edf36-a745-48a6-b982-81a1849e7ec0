
#include<stdio.h>

#define SIZE 5

void display_array_elements(int arr[]);//arr -> pointer to 1-d array
int array_sum(int arr[]);
int rec_array_sum(int arr[], int index );



int main(void)
{
    int arr[ SIZE ] = { 10,20,30,40,50 };

    display_array_elements(arr);

    printf("sum of array ele's is: %d\n", array_sum(arr) );
    printf("sum of array ele's is: %d\n", rec_array_sum(arr, 0 ) );//first time calling to rec function
    //rec_array_sum(arr) => simple (non-recursive) function call 
    //calling function: main( )
    //called function: rec_array_sum( )


    return 0;
}

void display_array_elements(int arr[])
{
    int index;
    printf("array ele's are: ");
    for( index = 0 ; index < SIZE ; index++ ){
        printf("%4d", arr[ index ] );
    }
    printf("\n");
}

//non-recursive function
int array_sum(int arr[ ])
{
    int sum = 0;
    int index = 0;

    for( index = 0 ; index < SIZE ; index++ ){
        sum += arr[ index ];       
    }
    return sum;
}

//recursive function
int rec_array_sum(int arr[], int index )
{
    //base condition
    if( index == SIZE )
        return 0;

    return ( arr[ index ] + rec_array_sum(arr, index+1 ) );
}


/*
    rec_array_sum(arr, index+1 );
    recursive function call => calling function & called function are same
    calling function => rec_array_sum( )
    called function  => rec_array_sum( ) 
*/












