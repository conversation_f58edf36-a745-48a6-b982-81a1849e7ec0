/* 
    sorting algorithms implementation:
    
    
==================================================================*/

#include<stdio.h>

#define SIZE 10

#define SWAP(a, b) { int t = a; a = b ; b = t ; }


void display_array_elements(int arr[]);//arr -> pointer to 1-d array
void selection_sort(int arr[]);
void bubble_sort(int arr[]);

int main(void)
{
    //int arr[ SIZE ] = { 30, 20, 60, 50, 10, 40 };
    //int arr[ SIZE ] = { 30, 90, 20, 60, 50, 70, 40, 100, 80, 10 };
    int arr[ SIZE ] = { 10, 20, 30, 40, 50, 60, 70, 80, 90, 100 };

    
    printf("bfore sorting: "); display_array_elements(arr);
    //selection_sort(arr);
    bubble_sort(arr);
    printf("after sorting: "); display_array_elements(arr);

    return 0;
}

void display_array_elements(int arr[])
{
    int index;
    printf("array ele's are: ");
    for( index = 0 ; index < SIZE ; index++ ){
        printf("%4d", arr[ index ] );
    }
    printf("\n");
}

void selection_sort(int arr[])
{
    int sel_pos;
    int pos;
    int iterations=0;
    int comparison=0;

    for( sel_pos = 0 ; sel_pos < SIZE-1 ; sel_pos++ )//outer loop is for iterations
    {
        iterations++;
        for( pos = sel_pos + 1 ; pos < SIZE ; pos++ )//innner for loop is pos
        {
            comparison++;
            //if ele at sel_pos found greater than ele at pos => swap them
            if( arr[ sel_pos ] > arr[ pos ] )
                SWAP(arr[ sel_pos ] , arr[ pos ] );
        }
    }

    printf("no. of iterations are: %d\n", iterations);
    printf("no. of comparisons are: %d\n", comparison);
}

void bubble_sort(int arr[])
{
    int it;
    int pos;
    int iterations=0;
    int comparison=0;
    int flag = 1;    


    for( it = 0 ; it < SIZE-1 && flag == 1 ; it++ )//outer for loop is for iterations
    {
        flag = 0;
        iterations++;
        for( pos = 0 ; pos < SIZE-it-1 ; pos++ )//for each iteration pos starts from 0 - 
        {
            comparison++;
            //if( prev pos ele > next pos ele ) => if pair is not in order => swap them
            if( arr[ pos ] > arr[ pos+1 ] )
            {
                flag = 1;
                SWAP(arr[ pos ], arr[ pos+1 ] );
            }
        }
    }

    printf("no. of iterations are: %d\n", iterations);
    printf("no. of comparisons are: %d\n", comparison);

}


/*
void bubble_sort(int arr[])
{
    int it;
    int pos;
    int iterations=0;
    int comparison=0;


    for( it = 0 ; it < SIZE-1 ; it++ )//outer for loop is for iterations
    {
        iterations++;
        for( pos = 0 ; pos < SIZE-it-1 ; pos++ )//for each iteration pos starts from 0 - 
        {
            comparison++;
            //if( prev pos ele > next pos ele ) => if pair is not in order => swap them
            if( arr[ pos ] > arr[ pos+1 ] )
                SWAP(arr[ pos ], arr[ pos+1 ] );
        }
    }

    printf("no. of iterations are: %d\n", iterations);
    printf("no. of comparisons are: %d\n", comparison);

}
*/








