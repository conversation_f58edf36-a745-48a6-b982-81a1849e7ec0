//implementation of singly circular linked list operations:

#include<stdio.h>
#include<stdlib.h>

typedef struct node
{
    int data;
    struct node *next;
}node_t;

typedef struct 
{
    node_t *head;
    int cnt;
}list_t;


typedef int bool_t;

void init_list(list_t *list);
bool_t is_list_empty(list_t *list);
node_t *create_node(int data);
void add_node_at_last_position(list_t *list, int data);
void display_list(list_t *list);





int main(void)
{
    list_t list1;

    init_list(&list1);

    add_node_at_last_position(&list1, 11);
    add_node_at_last_position(&list1, 22);
    add_node_at_last_position(&list1, 33);
    add_node_at_last_position(&list1, 44);
    add_node_at_last_position(&list1, 55);
    add_node_at_last_position(&list1, 66);

    display_list(&list1);


    return 0;
}

void init_list(list_t *list)
{
    list->head = NULL;
    list->cnt = 0;
}

bool_t is_list_empty(list_t *list)
{
    return ( list->head == NULL );
}

void display_list(list_t *list)
{
    //if list is not empty
    if( !is_list_empty(list) )
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node including it
        
        printf("head -> ");
        do
        {
            printf("%d -> ", trav->data);//display data part of node
            trav = trav->next;
        }while( trav != list->head );
        printf(" null\n");
        //printf("no. of nodes in a list are: %d\n", count_nodes(list) ); //=> O(n)
        printf("no. of nodes in a list are: %d\n", list->cnt );//=> O(1)
    }
    else
        printf("list is empty !!!\n");
}

node_t *create_node(int data)
{
    //1. allocate memory dynamically for a node
    node_t *temp = (node_t *)malloc( sizeof(node_t) );
    /* on success, malloc() function allocates requested bytes of memory from heap section and
    it returns starting addr of dynamically allocated block and on failure it returns NULL */
    if( temp == NULL )
    {
        perror("malloc() failed !!!\n");
        exit(1);
    }

    //2. initialize members of the node
    temp->data = data;
    temp->next = NULL;

    //3. return an addr of dynamically allocated node/block to the calling function
    return temp;//by meansreturninig value of pointer var we are returnning addr
}

void add_node_at_last_position(list_t *list, int data)
{
    //step-1: create a newnode
    node_t *newnode = create_node(data);

    //step-2: if list is empty => attach newly created node to the head
    if( is_list_empty(list) )
    {
        //store an addr of newly created node into the head
        list->head = newnode;
        //store an addr first node into the next part newly added node at last
        newnode->next = list->head;
        list->cnt++;
    }
    else//step-3: if list is not empty
    {
        //start traversal from first node
        node_t *trav = list->head;
        //traverse the list till last node
        while( trav->next != list->head )
        {
            trav = trav->next;//move trav towards its next node
        }

        /* attach newly created node to the last node i.e. to store an addr of newly created node
        into the next part of last node */
        trav->next = newnode;
        //store an addr first node into the next part newly added node at last
        newnode->next = list->head;
        list->cnt++;
       }
}






