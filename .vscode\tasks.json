{"version": "2.0.0", "tasks": [{"type": "cppbuild", "label": "C/C++: gcc.exe build active file", "command": "C:\\MinGW\\bin\\gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "compiler: C:\\MinGW\\bin\\gcc.exe"}, {"type": "cppbuild", "label": "C/C++: gcc.exe build active file", "command": "C:\\MinGW\\bin\\gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "compiler: C:\\MinGW\\bin\\gcc.exe"}, {"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "C:\\MinGW\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "compiler: C:\\MinGW\\bin\\g++.exe"}, {"type": "cppbuild", "label": "C/C++: gcc.exe build active file", "command": "C:\\MinGW\\bin\\gcc.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "compiler: C:\\MinGW\\bin\\gcc.exe"}, {"type": "cppbuild", "label": "C/C++: cpp.exe build active file", "command": "C:\\MinGW\\bin\\cpp.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": {"kind": "build", "isDefault": true}, "detail": "compiler: C:\\MinGW\\bin\\cpp.exe"}, {"type": "cppbuild", "label": "C/C++: g++.exe build active file", "command": "C:\\MinGW\\bin\\g++.exe", "args": ["-fdiagnostics-color=always", "-g", "${file}", "-o", "${fileDirname}\\${fileBasenameNoExtension}.exe"], "options": {"cwd": "${fileDirname}"}, "problemMatcher": ["$gcc"], "group": "build", "detail": "compiler: C:\\MinGW\\bin\\g++.exe"}]}