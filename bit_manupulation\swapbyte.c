#include<stdio.h>
#include<stdint.h>

uint32_t Swapbytes(uint32_t data)
{

    uint32_t output = 0x0;
    uint8_t temp = 0x0;

    for (int i = 0; i < 4; i++)
    {
        output = output << 8;
        temp = data >> (8 * i) & 0xff;
        output += temp;
    }

    return output;
}

int main()
{
    uint32_t input = 0xCC8700FC, output = Swapbytes(input);
    printf("intput 0x%x  and output 0x%x", input, output);

    return 0;
}