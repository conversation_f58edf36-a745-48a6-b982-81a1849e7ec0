/* filename: list.h
	this file contains declaration list structure & declarations of list functionalities
======================================================================================  */


#include"node.h"

typedef int bool_t;

//list structure
typedef struct
{
    node_t *head;//4 bytes    
    int cnt;
}list_t;

//declaration of list functionalities:
void init_list(list_t *list);
void add_node_at_last_position(list_t *list, int data);
void add_node_at_first_position(list_t *list, int data);
void add_node_at_specific_position(list_t *list, int pos, int data);
void delete_node_at_first_position(list_t *list);
void delete_node_at_last_position(list_t *list);
void delete_node_at_specific_position(list_t *list, int pos);
node_t *create_node(int data);
bool_t is_list_empty(list_t *list);
void display_list(list_t *list);
int count_nodes(list_t *list);
void free_list(list_t *list);
void display_list_reverse(list_t *list, node_t *trav);
void reverse_list(list_t *list);
bool_t search_and_delete(list_t *list, int data);
node_t *seach_node(list_t *list, node_t **prev, int data);
