#include<iostream>

using namespace std;
class solution
{
public:
  vector<int> non_repeting_number(vector<int> nums)
  {

   vector<int>ans;
   int k=nums.size();
   int xor =nums[0];
   for(int i=0;i<k;i++)
     {
      xor =xor ^nums[i];
     }
     int select_right_bit =xor & ~(xor-1);
     int x,y;
     x=y=0;

     for(int j=0;j<k;j++)
      {
      if(nums[j]&select_right_bit)
        {

         x=x ^nums[j];
          }
       else
        {
          y =y^nums[j];
         }
}
   ans.push_back(x);
   ans.push_back(y);
  sort(ans.begin(),ans.end());
   return ans;

}
};
int main()
{
int n;
cin>>n;
vector<int> v(2 *n+2);
for(inti=0;i< 2*n+2;i++)
{
    cin>>v[i];
}


int result;
result  = non_repeting_number(v);
for(auto i:result)
      cout<<i<< " ";
cout<<"\n";
//cout<<"nn reapting number is " <<result<<endl;

return 0;
}
