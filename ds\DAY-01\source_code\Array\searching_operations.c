/* 
    searching algorithms implementation:
    
    1. linear search
    2. binary search
==================================================================*/

#include<stdio.h>

#define SIZE 10

#define true 1
#define false 0

typedef int bool_t;

void display_array_elements(int arr[]);//arr -> pointer to 1-d array
int comparisons = 0;
bool_t linear_search(int arr[], int key );


int main(void)
{
    int arr[ SIZE ] = { 90, 70, 80, 10, 20, 30, 60, 40, 50, 100 };
    int key;

    display_array_elements(arr);

    printf("enter the key: ");
    scanf("%d", &key);


    if( linear_search( arr, key ) )
        printf("%d is found in an array, no. of comparisons = %d\n", key, comparisons);
    else    
        printf("%d is not found in an array, no. of comparisons = %d\n", key, comparisons);



    return 0;
}

void display_array_elements(int arr[])
{
    int index;
    printf("array ele's are: ");
    for( index = 0 ; index < SIZE ; index++ ){
        printf("%4d", arr[ index ] );
    }
    printf("\n");
}

bool_t linear_search(int arr[], int key )
{
    int index;
    comparisons=0;

    for( index = 0 ; index < SIZE ; index++ ){
        comparisons++;
        if( key == arr[ index ] )
            return true;//key is found        
    }

    return false;//key is not found
}
